# Security Exploitation Modules Requirements
# متطلبات وحدات استغلال الأمان

# Core system monitoring and process management
psutil>=5.9.0

# Windows-specific libraries for credential decryption and system access
pywin32>=306; sys_platform == "win32"

# Database support (usually included with Python)
# sqlite3 is part of Python standard library

# Machine Learning and AI (for Advanced Intelligence Module)
numpy>=1.21.0
pandas>=1.3.0
scikit-learn>=1.0.0

# Natural Language Processing
nltk>=3.7
textblob>=0.17.1

# Computer Vision
opencv-python>=4.5.0
pytesseract>=0.3.8
Pillow>=8.3.0

# File system monitoring
watchdog>=2.1.0

# Optional: Enhanced functionality
requests>=2.28.0
cryptography>=3.4.8
matplotlib>=3.5.0
seaborn>=0.11.0

# Development and testing
pytest>=7.0.0
pytest-cov>=4.0.0
